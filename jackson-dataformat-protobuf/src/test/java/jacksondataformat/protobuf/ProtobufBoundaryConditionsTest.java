package jacksondataformat.protobuf;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.BoolValue;
import com.google.protobuf.DoubleValue;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import pet.v1.Address;
import pet.v1.Owner;
import pet.v1.Pet;
import pet.v1.PetStatus;
import pet.v1.PetType;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("Protobuf Boundary Conditions Tests")
class ProtobufBoundaryConditionsTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new ProtobufModule());
    }

    @Nested
    @DisplayName("Null and Empty Value Tests")
    class NullAndEmptyValueTests {

        @Test
        @DisplayName("Should handle Pet with empty string fields")
        void shouldHandlePetWithEmptyStringFields() {
            // Arrange
            Pet pet = Pet.newBuilder()
                    .setId("")
                    .setName("")
                    .setType(PetType.PET_TYPE_UNSPECIFIED)
                    .setStatus(PetStatus.PET_STATUS_UNSPECIFIED)
                    .build();

            // Act
            String json = writeValueAsString(pet);
            Pet actual = readValue(json, Pet.class);

            // Assert
            assertThat(actual).isEqualTo(pet);
        }

        @Test
        @DisplayName("Should handle Pet with empty repeated fields")
        void shouldHandlePetWithEmptyRepeatedFields() {
            // Arrange
            Pet pet = Pet.newBuilder()
                    .setId("pet-123")
                    .setName("Empty Tags Pet")
                    .setType(PetType.DOG)
                    .setStatus(PetStatus.AVAILABLE)
                    .addAllTags(Collections.emptyList())
                    .addAllPreviousAddresses(Collections.emptyList())
                    .build();

            // Act
            String json = writeValueAsString(pet);
            Pet actual = readValue(json, Pet.class);

            // Assert
            assertThat(actual.getTagsList()).isEmpty();
        }

        @Test
        @DisplayName("Should handle Pet with empty map fields")
        void shouldHandlePetWithEmptyMapFields() {
            // Arrange
            Pet pet = Pet.newBuilder()
                    .setId("pet-123")
                    .setName("Empty Metadata Pet")
                    .setType(PetType.CAT)
                    .setStatus(PetStatus.AVAILABLE)
                    .putAllMetadata(Collections.emptyMap())
                    .build();

            // Act
            String json = writeValueAsString(pet);
            Pet actual = readValue(json, Pet.class);

            // Assert
            assertThat(actual.getMetadataMap()).isEmpty();
        }

        @Test
        @DisplayName("Should handle Pet without optional fields")
        void shouldHandlePetWithoutOptionalFields() {
            // Arrange
            Pet pet = Pet.newBuilder()
                    .setId("pet-123")
                    .setName("Minimal Pet")
                    .setType(PetType.FISH)
                    .setStatus(PetStatus.AVAILABLE)
                    .build();

            // Act
            String json = writeValueAsString(pet);
            Pet actual = readValue(json, Pet.class);

            // Assert
            assertThat(actual.hasOwner()).isFalse();
        }

        @Test
        @DisplayName("Should handle Address with empty fields")
        void shouldHandleAddressWithEmptyFields() {
            // Arrange
            Address address = Address.newBuilder()
                    .setStreet("")
                    .setCity("")
                    .setState("")
                    .setZipCode("")
                    .setCountry("")
                    .build();

            // Act
            String json = writeValueAsString(address);
            Address actual = readValue(json, Address.class);

            // Assert
            assertThat(actual.getStreet()).isEmpty();
        }

        @Test
        @DisplayName("Should handle Owner without optional phone")
        void shouldHandleOwnerWithoutOptionalPhone() {
            // Arrange
            Owner owner = Owner.newBuilder()
                    .setId("owner-123")
                    .setName("John Doe")
                    .setEmail("<EMAIL>")
                    .setAddress(Address.newBuilder()
                            .setStreet("123 Main St")
                            .setCity("Springfield")
                            .setState("IL")
                            .setZipCode("62701")
                            .setCountry("USA")
                            .build())
                    .build();

            // Act
            String json = writeValueAsString(owner);
            Owner actual = readValue(json, Owner.class);

            // Assert
            assertThat(actual.getPhone()).isEmpty();
        }
    }

    @Nested
    @DisplayName("Default Value Tests")
    class DefaultValueTests {

        @Test
        @DisplayName("Should use default values for unset fields")
        void shouldUseDefaultValuesForUnsetFields() {
            // Arrange
            Pet pet = Pet.newBuilder().build();

            // Act
            String json = writeValueAsString(pet);
            Pet actual = readValue(json, Pet.class);

            // Assert
            assertThat(actual.getId()).isEmpty();
            assertThat(actual.getName()).isEmpty();
            assertThat(actual.getType()).isEqualTo(PetType.PET_TYPE_UNSPECIFIED);
            assertThat(actual.getStatus()).isEqualTo(PetStatus.PET_STATUS_UNSPECIFIED);
        }

        @Test
        @DisplayName("Should handle well-known type default values")
        void shouldHandleWellKnownTypeDefaultValues() {
            // Arrange
            Pet pet = Pet.newBuilder()
                    .setId("pet-123")
                    .setName("Default Values Pet")
                    .setType(PetType.DOG)
                    .setStatus(PetStatus.AVAILABLE)
                    .setWeight(DoubleValue.of(0.0))
                    .setIsVaccinated(BoolValue.of(false))
                    .build();

            // Act
            String json = writeValueAsString(pet);
            Pet actual = readValue(json, Pet.class);

            // Assert
            assertThat(actual.getWeight().getValue()).isEqualTo(0.0);
        }
    }

    @Nested
    @DisplayName("Large Data Tests")
    class LargeDataTests {

        @Test
        @DisplayName("Should handle Pet with many tags")
        void shouldHandlePetWithManyTags() {
            // Arrange
            List<String> manyTags = List.of(
                    "tag1", "tag2", "tag3", "tag4", "tag5",
                    "tag6", "tag7", "tag8", "tag9", "tag10",
                    "tag11", "tag12", "tag13", "tag14", "tag15",
                    "tag16", "tag17", "tag18", "tag19", "tag20"
            );

            Pet pet = Pet.newBuilder()
                    .setId("pet-123")
                    .setName("Many Tags Pet")
                    .setType(PetType.DOG)
                    .setStatus(PetStatus.AVAILABLE)
                    .addAllTags(manyTags)
                    .build();

            // Act
            String json = writeValueAsString(pet);
            Pet actual = readValue(json, Pet.class);

            // Assert
            assertThat(actual.getTagsList()).hasSize(20);
        }

        @Test
        @DisplayName("Should handle Pet with large metadata map")
        void shouldHandlePetWithLargeMetadataMap() {
            // Arrange
            Map<String, String> largeMetadata = Map.of(
                    "key1", "value1", "key2", "value2", "key3", "value3",
                    "key4", "value4", "key5", "value5", "key6", "value6",
                    "key7", "value7", "key8", "value8", "key9", "value9",
                    "key10", "value10"
            );

            Pet pet = Pet.newBuilder()
                    .setId("pet-123")
                    .setName("Large Metadata Pet")
                    .setType(PetType.CAT)
                    .setStatus(PetStatus.AVAILABLE)
                    .putAllMetadata(largeMetadata)
                    .build();

            // Act
            String json = writeValueAsString(pet);
            Pet actual = readValue(json, Pet.class);

            // Assert
            assertThat(actual.getMetadataMap()).hasSize(10);
        }

        @Test
        @DisplayName("Should handle Pet with long string values")
        void shouldHandlePetWithLongStringValues() {
            // Arrange
            String longString = "a".repeat(1000);
            Pet pet = Pet.newBuilder()
                    .setId(longString)
                    .setName(longString)
                    .setType(PetType.BIRD)
                    .setStatus(PetStatus.AVAILABLE)
                    .build();

            // Act
            String json = writeValueAsString(pet);
            Pet actual = readValue(json, Pet.class);

            // Assert
            assertThat(actual.getId()).hasSize(1000);
        }
    }

    @SneakyThrows
    private String writeValueAsString(Object value) {
        return objectMapper.writeValueAsString(value);
    }

    @SneakyThrows
    private <T> T readValue(String json, Class<T> clazz) {
        return objectMapper.readValue(json, clazz);
    }
}
