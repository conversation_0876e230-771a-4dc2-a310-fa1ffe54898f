package jacksondataformat.protobuf;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.BoolValue;
import com.google.protobuf.DoubleValue;
import com.google.protobuf.Duration;
import com.google.protobuf.Timestamp;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import pet.v1.Address;
import pet.v1.Owner;
import pet.v1.Pet;
import pet.v1.PetStatus;
import pet.v1.PetType;

import java.time.Instant;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@DisplayName("ProtobufModule Tests")
class ProtobufModuleTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new ProtobufModule());
    }

    @Nested
    @DisplayName("Message Serialization Tests")
    class MessageSerializationTests {

        @Test
        @DisplayName("Should serialize simple Pet message to JSON with visible output")
        void shouldSerializeSimplePetMessage() {
            // Arrange
            Pet pet = Pet.newBuilder()
                    .setId("pet-123")
                    .setName("Fluffy")
                    .setType(PetType.CAT)
                    .setStatus(PetStatus.AVAILABLE)
                    .build();

            // Act
            String actualJson = writeValueAsString(pet);

            // Assert & Display
            System.out.println("🐱 Simple Pet JSON Output:");
            System.out.println(actualJson);
            System.out.println();

            assertThat(actualJson).contains("\"id\":\"pet-123\"");
            assertThat(actualJson).contains("\"name\":\"Fluffy\"");
            assertThat(actualJson).contains("\"type\":\"CAT\"");
            assertThat(actualJson).contains("\"status\":\"AVAILABLE\"");
        }

        @Test
        @DisplayName("Should serialize Pet with nested objects showing complete JSON structure")
        void shouldSerializePetWithNestedObjects() {
            // Arrange
            Address address = Address.newBuilder()
                    .setStreet("123 Main St")
                    .setCity("Springfield")
                    .setState("IL")
                    .setZipCode("62701")
                    .setCountry("USA")
                    .build();

            Owner owner = Owner.newBuilder()
                    .setId("owner-456")
                    .setName("John Doe")
                    .setEmail("<EMAIL>")
                    .setPhone("555-1234")
                    .setAddress(address)
                    .build();

            Pet pet = Pet.newBuilder()
                    .setId("pet-123")
                    .setName("Buddy")
                    .setType(PetType.DOG)
                    .setStatus(PetStatus.SOLD)
                    .setOwner(owner)
                    .build();

            // Act
            String actualJson = writeValueAsString(pet);

            // Assert & Display
            System.out.println("🐕 Pet with Nested Owner JSON Output:");
            System.out.println(actualJson);
            System.out.println();

            assertThat(actualJson).contains("\"owner\"");
            assertThat(actualJson).contains("\"address\"");
            assertThat(actualJson).contains("\"street\":\"123 Main St\"");
        }

        @Test
        @DisplayName("Should serialize Pet with complex fields showing all data types")
        void shouldSerializePetWithComplexFields() {
            // Arrange
            Instant birthDate = Instant.parse("2020-01-15T10:30:00Z");
            Pet pet = Pet.newBuilder()
                    .setId("pet-123")
                    .setName("Max")
                    .setType(PetType.DOG)
                    .setStatus(PetStatus.AVAILABLE)
                    .addAllTags(List.of("friendly", "trained", "vaccinated"))
                    .setBirthDate(Timestamp.newBuilder()
                            .setSeconds(birthDate.getEpochSecond())
                            .setNanos(birthDate.getNano())
                            .build())
                    .setLifeExpectancy(Duration.newBuilder()
                            .setSeconds(12 * 365 * 24 * 60 * 60) // 12 years
                            .build())
                    .setWeight(DoubleValue.of(25.5))
                    .setIsVaccinated(BoolValue.of(true))
                    .putAllMetadata(Map.of(
                            "breed", "Golden Retriever",
                            "color", "Golden",
                            "microchip", "123456789"
                    ))
                    .build();

            // Act
            String actualJson = writeValueAsString(pet);

            // Assert & Display
            System.out.println("🐕 Pet with Complex Fields JSON Output:");
            System.out.println(actualJson);
            System.out.println();

            assertThat(actualJson).contains("\"tags\":[\"friendly\",\"trained\",\"vaccinated\"]");
            assertThat(actualJson).contains("\"birthDate\":\"2020-01-15T10:30:00Z\"");
            assertThat(actualJson).contains("\"lifeExpectancy\":\"378432000s\"");
            assertThat(actualJson).contains("\"weight\":25.5");
            assertThat(actualJson).contains("\"isVaccinated\":true");
            assertThat(actualJson).contains("\"metadata\"");
        }
    }

    @Nested
    @DisplayName("Message Deserialization Tests")
    class MessageDeserializationTests {

        @Test
        @DisplayName("Should deserialize simple Pet message from JSON with input/output display")
        void shouldDeserializeSimplePetMessage() {
            // Arrange
            String inputJson = """
                    {
                        "id": "pet-456",
                        "name": "Whiskers",
                        "type": "CAT",
                        "status": "PENDING"
                    }
                    """;

            // Act
            Pet actualPet = readValue(inputJson, Pet.class);

            // Assert & Display
            System.out.println("🐱 Simple Pet Deserialization:");
            System.out.println("Input JSON:");
            System.out.println(inputJson);
            System.out.println("Output Pet Object:");
            System.out.println("  ID: " + actualPet.getId());
            System.out.println("  Name: " + actualPet.getName());
            System.out.println("  Type: " + actualPet.getType());
            System.out.println("  Status: " + actualPet.getStatus());
            System.out.println();

            assertThat(actualPet.getId()).isEqualTo("pet-456");
            assertThat(actualPet.getName()).isEqualTo("Whiskers");
            assertThat(actualPet.getType()).isEqualTo(PetType.CAT);
            assertThat(actualPet.getStatus()).isEqualTo(PetStatus.PENDING);
        }

        @Test
        @DisplayName("Should deserialize Pet with nested objects showing complete structure")
        void shouldDeserializePetWithNestedObjects() {
            // Arrange
            String inputJson = """
                    {
                        "id": "pet-789",
                        "name": "Rex",
                        "type": "DOG",
                        "status": "SOLD",
                        "owner": {
                            "id": "owner-123",
                            "name": "Alice Smith",
                            "email": "<EMAIL>",
                            "phone": "555-9876",
                            "address": {
                                "street": "456 Oak Ave",
                                "city": "Portland",
                                "state": "OR",
                                "zipCode": "97201",
                                "country": "USA"
                            }
                        }
                    }
                    """;

            // Act
            Pet actualPet = readValue(inputJson, Pet.class);

            // Assert & Display
            System.out.println("🐕 Pet with Nested Objects Deserialization:");
            System.out.println("Input JSON:");
            System.out.println(inputJson);
            System.out.println("Output Pet Object:");
            System.out.println("  Pet ID: " + actualPet.getId());
            System.out.println("  Pet Name: " + actualPet.getName());
            System.out.println("  Has Owner: " + actualPet.hasOwner());
            if (actualPet.hasOwner()) {
                Owner owner = actualPet.getOwner();
                System.out.println("  Owner Name: " + owner.getName());
                System.out.println("  Owner Email: " + owner.getEmail());
                System.out.println("  Owner Address: " + owner.getAddress().getStreet() + ", " + owner.getAddress().getCity());
            }
            System.out.println();

            assertThat(actualPet.hasOwner()).isTrue();
            assertThat(actualPet.getOwner().getName()).isEqualTo("Alice Smith");
            assertThat(actualPet.getOwner().getAddress().getCity()).isEqualTo("Portland");
        }

        @Test
        @DisplayName("Should deserialize Pet with complex fields showing all data types")
        void shouldDeserializePetWithComplexFields() {
            // Arrange
            String inputJson = """
                    {
                        "id": "pet-999",
                        "name": "Luna",
                        "type": "DOG",
                        "status": "AVAILABLE",
                        "tags": ["playful", "energetic", "house-trained"],
                        "birthDate": "2019-03-20T14:30:00Z",
                        "lifeExpectancy": "473040000s",
                        "weight": 18.7,
                        "isVaccinated": true,
                        "metadata": {
                            "breed": "Border Collie",
                            "color": "Black and White",
                            "registration": "AKC-12345"
                        }
                    }
                    """;

            // Act
            Pet actualPet = readValue(inputJson, Pet.class);

            // Assert & Display
            System.out.println("🐕 Pet with Complex Fields Deserialization:");
            System.out.println("Input JSON:");
            System.out.println(inputJson);
            System.out.println("Output Pet Object:");
            System.out.println("  Pet ID: " + actualPet.getId());
            System.out.println("  Pet Name: " + actualPet.getName());
            System.out.println("  Tags: " + actualPet.getTagsList());
            System.out.println("  Birth Date: " + (actualPet.hasBirthDate() ? actualPet.getBirthDate() : "Not set"));
            System.out.println("  Weight: " + (actualPet.hasWeight() ? actualPet.getWeight().getValue() : "Not set"));
            System.out.println("  Is Vaccinated: " + (actualPet.hasIsVaccinated() ? actualPet.getIsVaccinated().getValue() : "Not set"));
            System.out.println("  Metadata: " + actualPet.getMetadataMap());
            System.out.println();

            assertThat(actualPet.getTagsList()).containsExactly("playful", "energetic", "house-trained");
            assertThat(actualPet.hasBirthDate()).isTrue();
            assertThat(actualPet.hasWeight()).isTrue();
            assertThat(actualPet.getWeight().getValue()).isEqualTo(18.7);
            assertThat(actualPet.getMetadataMap()).hasSize(3);
        }
    }

    @Nested
    @DisplayName("Enum Serialization and Deserialization Tests")
    class EnumTests {

        @Test
        @DisplayName("Should serialize and deserialize protobuf enums with visible output")
        void shouldSerializeAndDeserializeProtobufEnums() {
            // Arrange
            PetType dogType = PetType.DOG;
            PetStatus availableStatus = PetStatus.AVAILABLE;

            // Act - Serialize
            String dogTypeJson = writeValueAsString(dogType);
            String statusJson = writeValueAsString(availableStatus);

            // Display Serialization
            System.out.println("🏷️ Enum Serialization:");
            System.out.println("PetType.DOG -> " + dogTypeJson);
            System.out.println("PetStatus.AVAILABLE -> " + statusJson);

            // Act - Deserialize
            PetType deserializedType = readValue("\"CAT\"", PetType.class);
            PetStatus deserializedStatus = readValue("\"PENDING\"", PetStatus.class);

            // Display Deserialization
            System.out.println("\"CAT\" -> " + deserializedType);
            System.out.println("\"PENDING\" -> " + deserializedStatus);
            System.out.println();

            // Assert
            assertThat(dogTypeJson).isEqualTo("\"DOG\"");
            assertThat(statusJson).isEqualTo("\"AVAILABLE\"");
            assertThat(deserializedType).isEqualTo(PetType.CAT);
            assertThat(deserializedStatus).isEqualTo(PetStatus.PENDING);
        }

        @Test
        @DisplayName("Should handle enum deserialization from numbers")
        void shouldDeserializeEnumsFromNumbers() {
            // Arrange & Act
            PetType typeFromNumber = readValue("2", PetType.class); // CAT = 2
            PetStatus statusFromNumber = readValue("1", PetStatus.class); // AVAILABLE = 1

            // Display
            System.out.println("🔢 Enum Deserialization from Numbers:");
            System.out.println("Number 2 -> " + typeFromNumber);
            System.out.println("Number 1 -> " + statusFromNumber);
            System.out.println();

            // Assert
            assertThat(typeFromNumber).isEqualTo(PetType.CAT);
            assertThat(statusFromNumber).isEqualTo(PetStatus.AVAILABLE);
        }

        @Test
        @DisplayName("Should handle unknown enum values as UNRECOGNIZED")
        void shouldHandleUnknownEnumValues() {
            // Act
            PetType unknownType = readValue("999", PetType.class);
            PetStatus unknownStatus = readValue("\"UNKNOWN_STATUS\"", PetStatus.class);

            // Display
            System.out.println("❓ Unknown Enum Values:");
            System.out.println("Unknown number 999 -> " + unknownType);
            System.out.println("Unknown string \"UNKNOWN_STATUS\" -> " + unknownStatus);
            System.out.println();

            // Assert
            assertThat(unknownType).isEqualTo(PetType.UNRECOGNIZED);
            assertThat(unknownStatus).isEqualTo(PetStatus.UNRECOGNIZED);
        }
    }

    @Nested
    @DisplayName("Round Trip Consistency Tests")
    class RoundTripTests {

        @Test
        @DisplayName("Should maintain data integrity through serialize-deserialize cycle")
        void shouldMaintainDataIntegrityThroughRoundTrip() {
            // Arrange
            Pet originalPet = Pet.newBuilder()
                    .setId("round-trip-test")
                    .setName("Consistency Pet")
                    .setType(PetType.BIRD)
                    .setStatus(PetStatus.SOLD)
                    .addAllTags(List.of("test", "round-trip"))
                    .putAllMetadata(Map.of("test", "value"))
                    .build();

            // Act
            String json = writeValueAsString(originalPet);
            Pet deserializedPet = readValue(json, Pet.class);

            // Display
            System.out.println("🔄 Round Trip Test:");
            System.out.println("Original Pet: " + originalPet.getName() + " (" + originalPet.getType() + ")");
            System.out.println("JSON: " + json);
            System.out.println("Deserialized Pet: " + deserializedPet.getName() + " (" + deserializedPet.getType() + ")");
            System.out.println("Data Integrity: " + originalPet.equals(deserializedPet));
            System.out.println();

            // Assert
            assertThat(deserializedPet).isEqualTo(originalPet);
        }
    }

    @Nested
    @DisplayName("Module Configuration Tests")
    class ModuleConfigurationTests {

        @Test
        @DisplayName("Should work correctly with ProtobufModule registered")
        void shouldWorkWithProtobufModuleRegistered() {
            // Arrange
            ObjectMapper mapperWithModule = new ObjectMapper();
            mapperWithModule.registerModule(new ProtobufModule());

            Pet pet = Pet.newBuilder()
                    .setId("config-test")
                    .setName("Module Test Pet")
                    .setType(PetType.FISH)
                    .setStatus(PetStatus.AVAILABLE)
                    .build();

            // Act & Assert
            String json = writeValueAsString(mapperWithModule, pet);
            Pet deserializedPet = readValue(mapperWithModule, json, Pet.class);

            // Display
            System.out.println("⚙️ Module Configuration Test:");
            System.out.println("With ProtobufModule - Success: " + pet.equals(deserializedPet));
            System.out.println();

            assertThat(deserializedPet).isEqualTo(pet);
        }

        @Test
        @DisplayName("Should fail without ProtobufModule for protobuf types")
        void shouldFailWithoutProtobufModuleForProtobufTypes() {
            // Arrange
            ObjectMapper mapperWithoutModule = new ObjectMapper();
            Pet pet = Pet.newBuilder()
                    .setId("fail-test")
                    .setName("Should Fail")
                    .setType(PetType.DOG)
                    .setStatus(PetStatus.AVAILABLE)
                    .build();

            // Act & Assert
            System.out.println("❌ Without ProtobufModule Test:");
            assertThatThrownBy(() -> writeValueAsString(mapperWithoutModule, pet))
                    .isInstanceOf(Exception.class);
            System.out.println("Without ProtobufModule - Expected failure occurred");
            System.out.println();
        }
    }

    @SneakyThrows
    private String writeValueAsString(Object value) {
        return objectMapper.writeValueAsString(value);
    }

    @SneakyThrows
    private String writeValueAsString(ObjectMapper mapper, Object value) {
        return mapper.writeValueAsString(value);
    }

    @SneakyThrows
    private <T> T readValue(String json, Class<T> clazz) {
        return objectMapper.readValue(json, clazz);
    }

    @SneakyThrows
    private <T> T readValue(ObjectMapper mapper, String json, Class<T> clazz) {
        return mapper.readValue(json, clazz);
    }
}
