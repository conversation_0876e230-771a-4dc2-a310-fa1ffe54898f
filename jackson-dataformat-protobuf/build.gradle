dependencies {
    compileOnly("com.fasterxml.jackson.core:jackson-databind:${jacksonVersion}")
    api("com.google.protobuf:protobuf-java-util:${protobufVersion}")

    // Test dependencies
    testImplementation("org.springframework.boot:spring-boot-starter-test:${springBootVersion}")
    testImplementation("com.fasterxml.jackson.core:jackson-databind:${jacksonVersion}")
}

apply from: "${rootDir}/gradle/deploy.gradle"
apply from: "${rootDir}/gradle/protobuf.gradle"