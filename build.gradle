plugins {
    id "org.springframework.boot" version "${springBootVersion}" apply false
    id "com.diffplug.spotless" version "${spotlessVersion}" apply false
    id "com.github.spotbugs" version "${spotbugsVersion}" apply false
    id "com.google.protobuf" version "${protobufGradlePluginVersion}" apply false
}

allprojects {
    apply plugin: "java"
    apply plugin: "java-library"

    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(17)
        }
    }

    repositories {
        mavenCentral()
        maven { url = "https://repo.spring.io/snapshot" }
        maven { url = "https://repo.spring.io/milestone" }
    }

    compileJava {
        options.encoding = "UTF-8"
        options.compilerArgs << "-parameters"
    }
    compileTestJava {
        options.encoding = "UTF-8"
        options.compilerArgs << "-parameters"
    }

    dependencies {
        compileOnly("org.projectlombok:lombok:${lombokVersion}")
        annotationProcessor("org.projectlombok:lombok:${lombokVersion}")
        testCompileOnly("org.projectlombok:lombok:${lombokVersion}")
        testAnnotationProcessor("org.projectlombok:lombok:${lombokVersion}")
        compileOnly("com.github.spotbugs:spotbugs-annotations:${spotbugsAnnotationsVersion}")
    }

    test {
        useJUnitPlatform()
        dependencies {
            // https://docs.gradle.org/current/userguide/upgrading_version_8.html#test_framework_implementation_dependencies
            testRuntimeOnly("org.junit.platform:junit-platform-launcher:${junitPlatformVersion}")
        }
    }

    // spotless
    apply plugin: "com.diffplug.spotless"
    spotless {
        encoding "UTF-8"
        java {
            toggleOffOn()
            removeUnusedImports()
            trimTrailingWhitespace()
            endWithNewline()
            palantirJavaFormat()

            targetExclude "**/generated/**"

            custom("Refuse wildcard imports", {
                if (it =~ /\nimport .*\*;/) {
                    throw new IllegalStateException("Do not use wildcard imports, 'spotlessApply' cannot resolve this issue, please fix it manually.")
                }
            } as Closure<String>)
        }
    }

    // spotbugs
    apply plugin: "com.github.spotbugs"
    spotbugs {
        spotbugsTest.enabled = false
        omitVisitors.addAll "FindReturnRef", "DontReusePublicIdentifiers"
        excludeFilter = file("${rootDir}/config/spotbugs/exclude.xml")
    }
}